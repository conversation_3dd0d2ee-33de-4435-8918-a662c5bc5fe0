{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\llmNode.js\";\n// llmNode.js\n\nimport { BaseNode, createHandle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const LLMNode = ({\n  id,\n  data\n}) => {\n  const handles = [createHandle(`${id}-system`, 'target', Position.Left, {\n    top: `${100 / 3}%`\n  }), createHandle(`${id}-prompt`, 'target', Position.Left, {\n    top: `${200 / 3}%`\n  }), createHandle(`${id}-response`, 'source', Position.Right)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"LLM\",\n    handles: handles,\n    nodeType: \"llm\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '12px',\n        color: '#ffffff',\n        fontWeight: '500',\n        textAlign: 'center',\n        opacity: 0.9\n      },\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Large Language Model\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_c = LLMNode;\nvar _c;\n$RefreshReg$(_c, \"LLMNode\");", "map": {"version": 3, "names": ["BaseNode", "createHandle", "Position", "jsxDEV", "_jsxDEV", "LLMNode", "id", "data", "handles", "Left", "top", "Right", "title", "nodeType", "children", "style", "fontSize", "color", "fontWeight", "textAlign", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/llmNode.js"], "sourcesContent": ["// llmNode.js\n\nimport { BaseNode, createHandle } from './BaseNode';\nimport { Position } from 'reactflow';\n\nexport const LLMNode = ({ id, data }) => {\n  const handles = [\n    createHandle(`${id}-system`, 'target', Position.Left, { top: `${100/3}%` }),\n    createHandle(`${id}-prompt`, 'target', Position.Left, { top: `${200/3}%` }),\n    createHandle(`${id}-response`, 'source', Position.Right)\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"LLM\"\n      handles={handles}\n      nodeType=\"llm\"\n    >\n      <div style={{\n        fontSize: '12px',\n        color: '#ffffff',\n        fontWeight: '500',\n        textAlign: 'center',\n        opacity: 0.9\n      }}>\n        <span>Large Language Model</span>\n      </div>\n    </BaseNode>\n  );\n}\n"], "mappings": ";AAAA;;AAEA,SAASA,QAAQ,EAAEC,YAAY,QAAQ,YAAY;AACnD,SAASC,QAAQ,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,OAAO,MAAMC,OAAO,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EACvC,MAAMC,OAAO,GAAG,CACdP,YAAY,CAAE,GAAEK,EAAG,SAAQ,EAAE,QAAQ,EAAEJ,QAAQ,CAACO,IAAI,EAAE;IAAEC,GAAG,EAAG,GAAE,GAAG,GAAC,CAAE;EAAG,CAAC,CAAC,EAC3ET,YAAY,CAAE,GAAEK,EAAG,SAAQ,EAAE,QAAQ,EAAEJ,QAAQ,CAACO,IAAI,EAAE;IAAEC,GAAG,EAAG,GAAE,GAAG,GAAC,CAAE;EAAG,CAAC,CAAC,EAC3ET,YAAY,CAAE,GAAEK,EAAG,WAAU,EAAE,QAAQ,EAAEJ,QAAQ,CAACS,KAAK,CAAC,CACzD;EAED,oBACEP,OAAA,CAACJ,QAAQ;IACPM,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXK,KAAK,EAAC,KAAK;IACXJ,OAAO,EAAEA,OAAQ;IACjBK,QAAQ,EAAC,KAAK;IAAAC,QAAA,eAEdV,OAAA;MAAKW,KAAK,EAAE;QACVC,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE,QAAQ;QACnBC,OAAO,EAAE;MACX,CAAE;MAAAN,QAAA,eACAV,OAAA;QAAAU,QAAA,EAAM;MAAoB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAAC,EAAA,GA1BYpB,OAAO;AAAA,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}