{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\submit.js\",\n  _s = $RefreshSig$();\n// submit.js\n\nimport { useState } from 'react';\nimport { useStore } from './store';\nimport { shallow } from 'zustand/shallow';\nimport { PipelineAnalysisModal, ErrorModal } from './components/Modal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst selector = state => ({\n  nodes: state.nodes,\n  edges: state.edges\n});\nexport const SubmitButton = () => {\n  _s();\n  const {\n    nodes,\n    edges\n  } = useStore(selector, shallow);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const [analysisResult, setAnalysisResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isErrorModalOpen, setIsErrorModalOpen] = useState(false);\n  const [errorMessage, setErrorMessage] = useState(null);\n  const handleSubmit = async () => {\n    setIsLoading(true);\n    try {\n      // Prepare pipeline data\n      const pipelineData = {\n        nodes: nodes,\n        edges: edges\n      };\n\n      // Send to backend\n      const response = await fetch('http://localhost:8000/pipelines/parse', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(pipelineData)\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      const result = await response.json();\n\n      // Show modal with results\n      setAnalysisResult(result);\n      setIsModalOpen(true);\n    } catch (error) {\n      console.error('Error submitting pipeline:', error);\n      // Use consistent modal-based error handling\n      setErrorMessage(`Error submitting pipeline: ${error.message}`);\n      setIsErrorModalOpen(true);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const closeModal = () => {\n    setIsModalOpen(false);\n    setAnalysisResult(null);\n  };\n  const closeErrorModal = () => {\n    setIsErrorModalOpen(false);\n    setErrorMessage(null);\n  };\n  const hasNodes = nodes.length > 0;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"submit-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"submit-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pipeline-stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-number\",\n              children: nodes.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Nodes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-divider\",\n            children: \"\\u2022\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-number\",\n              children: edges.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"stat-label\",\n              children: \"Connections\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 21\n        }, this), !hasNodes && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"getting-started\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"start-hint\",\n            children: \"\\uD83D\\uDC46 Drag nodes from above to get started\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        className: `submit-button ${!hasNodes || isLoading ? 'disabled' : ''}`,\n        onClick: handleSubmit,\n        disabled: !hasNodes || isLoading,\n        children: isLoading ? /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 29\n          }, this), \"Analyzing...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 25\n        }, this) : hasNodes ? 'Analyze Pipeline' : 'Build Your Pipeline'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(PipelineAnalysisModal, {\n      isOpen: isModalOpen,\n      onClose: closeModal,\n      analysisResult: analysisResult\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ErrorModal, {\n      isOpen: isErrorModalOpen,\n      onClose: closeErrorModal,\n      errorMessage: errorMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(SubmitButton, \"+VK18wNyEvt0hK1j3H8EjHWz/Fo=\", false, function () {\n  return [useStore];\n});\n_c = SubmitButton;\nvar _c;\n$RefreshReg$(_c, \"SubmitButton\");", "map": {"version": 3, "names": ["useState", "useStore", "shallow", "PipelineAnalysisModal", "ErrorModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "selector", "state", "nodes", "edges", "SubmitButton", "_s", "isModalOpen", "setIsModalOpen", "analysisResult", "setAnalysisResult", "isLoading", "setIsLoading", "isErrorModalOpen", "setIsErrorModalOpen", "errorMessage", "setErrorMessage", "handleSubmit", "pipelineData", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "status", "result", "json", "error", "console", "message", "closeModal", "closeErrorModal", "hasNodes", "length", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onClick", "disabled", "style", "display", "alignItems", "gap", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/submit.js"], "sourcesContent": ["// submit.js\n\nimport { useState } from 'react';\nimport { useStore } from './store';\nimport { shallow } from 'zustand/shallow';\nimport { PipelineAnalysisModal, ErrorModal } from './components/Modal';\n\nconst selector = (state) => ({\n  nodes: state.nodes,\n  edges: state.edges,\n});\n\nexport const SubmitButton = () => {\n    const { nodes, edges } = useStore(selector, shallow);\n    const [isModalOpen, setIsModalOpen] = useState(false);\n    const [analysisResult, setAnalysisResult] = useState(null);\n    const [isLoading, setIsLoading] = useState(false);\n    const [isErrorModalOpen, setIsErrorModalOpen] = useState(false);\n    const [errorMessage, setErrorMessage] = useState(null);\n\n    const handleSubmit = async () => {\n        setIsLoading(true);\n        try {\n            // Prepare pipeline data\n            const pipelineData = {\n                nodes: nodes,\n                edges: edges\n            };\n\n            // Send to backend\n            const response = await fetch('http://localhost:8000/pipelines/parse', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                },\n                body: JSON.stringify(pipelineData)\n            });\n\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n\n            const result = await response.json();\n\n            // Show modal with results\n            setAnalysisResult(result);\n            setIsModalOpen(true);\n\n        } catch (error) {\n            console.error('Error submitting pipeline:', error);\n            // Use consistent modal-based error handling\n            setErrorMessage(`Error submitting pipeline: ${error.message}`);\n            setIsErrorModalOpen(true);\n        } finally {\n            setIsLoading(false);\n        }\n    };\n\n    const closeModal = () => {\n        setIsModalOpen(false);\n        setAnalysisResult(null);\n    };\n\n    const closeErrorModal = () => {\n        setIsErrorModalOpen(false);\n        setErrorMessage(null);\n    };\n\n    const hasNodes = nodes.length > 0;\n\n    return (\n        <>\n            <div className=\"submit-container\">\n                <div className=\"submit-info\">\n                    <div className=\"pipeline-stats\">\n                        <span className=\"stat-item\">\n                            <span className=\"stat-number\">{nodes.length}</span>\n                            <span className=\"stat-label\">Nodes</span>\n                        </span>\n                        <span className=\"stat-divider\">•</span>\n                        <span className=\"stat-item\">\n                            <span className=\"stat-number\">{edges.length}</span>\n                            <span className=\"stat-label\">Connections</span>\n                        </span>\n                    </div>\n                    {!hasNodes && (\n                        <div className=\"getting-started\">\n                            <span className=\"start-hint\">👆 Drag nodes from above to get started</span>\n                        </div>\n                    )}\n                </div>\n                <button\n                    type=\"button\"\n                    className={`submit-button ${!hasNodes || isLoading ? 'disabled' : ''}`}\n                    onClick={handleSubmit}\n                    disabled={!hasNodes || isLoading}\n                >\n                    {isLoading ? (\n                        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                            <div className=\"loading-spinner\"></div>\n                            Analyzing...\n                        </span>\n                    ) : hasNodes ? 'Analyze Pipeline' : 'Build Your Pipeline'}\n                </button>\n            </div>\n\n            <PipelineAnalysisModal\n                isOpen={isModalOpen}\n                onClose={closeModal}\n                analysisResult={analysisResult}\n            />\n\n            <ErrorModal\n                isOpen={isErrorModalOpen}\n                onClose={closeErrorModal}\n                errorMessage={errorMessage}\n            />\n        </>\n    );\n}\n"], "mappings": ";;AAAA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,QAAQ,SAAS;AAClC,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,qBAAqB,EAAEC,UAAU,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEvE,MAAMC,QAAQ,GAAIC,KAAK,KAAM;EAC3BC,KAAK,EAAED,KAAK,CAACC,KAAK;EAClBC,KAAK,EAAEF,KAAK,CAACE;AACf,CAAC,CAAC;AAEF,OAAO,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEH,KAAK;IAAEC;EAAM,CAAC,GAAGX,QAAQ,CAACQ,QAAQ,EAAEP,OAAO,CAAC;EACpD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMyB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7BL,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACA;MACA,MAAMM,YAAY,GAAG;QACjBf,KAAK,EAAEA,KAAK;QACZC,KAAK,EAAEA;MACX,CAAC;;MAED;MACA,MAAMe,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuC,EAAE;QAClEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACL,cAAc,EAAE;QACpB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACP,YAAY;MACrC,CAAC,CAAC;MAEF,IAAI,CAACC,QAAQ,CAACO,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAE,uBAAsBR,QAAQ,CAACS,MAAO,EAAC,CAAC;MAC7D;MAEA,MAAMC,MAAM,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;;MAEpC;MACApB,iBAAiB,CAACmB,MAAM,CAAC;MACzBrB,cAAc,CAAC,IAAI,CAAC;IAExB,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACAf,eAAe,CAAE,8BAA6Be,KAAK,CAACE,OAAQ,EAAC,CAAC;MAC9DnB,mBAAmB,CAAC,IAAI,CAAC;IAC7B,CAAC,SAAS;MACNF,YAAY,CAAC,KAAK,CAAC;IACvB;EACJ,CAAC;EAED,MAAMsB,UAAU,GAAGA,CAAA,KAAM;IACrB1B,cAAc,CAAC,KAAK,CAAC;IACrBE,iBAAiB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMyB,eAAe,GAAGA,CAAA,KAAM;IAC1BrB,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,eAAe,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMoB,QAAQ,GAAGjC,KAAK,CAACkC,MAAM,GAAG,CAAC;EAEjC,oBACIvC,OAAA,CAAAE,SAAA;IAAAsC,QAAA,gBACIxC,OAAA;MAAKyC,SAAS,EAAC,kBAAkB;MAAAD,QAAA,gBAC7BxC,OAAA;QAAKyC,SAAS,EAAC,aAAa;QAAAD,QAAA,gBACxBxC,OAAA;UAAKyC,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAC3BxC,OAAA;YAAMyC,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACvBxC,OAAA;cAAMyC,SAAS,EAAC,aAAa;cAAAD,QAAA,EAAEnC,KAAK,CAACkC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnD7C,OAAA;cAAMyC,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACP7C,OAAA;YAAMyC,SAAS,EAAC,cAAc;YAAAD,QAAA,EAAC;UAAC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvC7C,OAAA;YAAMyC,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACvBxC,OAAA;cAAMyC,SAAS,EAAC,aAAa;cAAAD,QAAA,EAAElC,KAAK,CAACiC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnD7C,OAAA;cAAMyC,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACL,CAACP,QAAQ,iBACNtC,OAAA;UAAKyC,SAAS,EAAC,iBAAiB;UAAAD,QAAA,eAC5BxC,OAAA;YAAMyC,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAuC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACN7C,OAAA;QACI8C,IAAI,EAAC,QAAQ;QACbL,SAAS,EAAG,iBAAgB,CAACH,QAAQ,IAAIzB,SAAS,GAAG,UAAU,GAAG,EAAG,EAAE;QACvEkC,OAAO,EAAE5B,YAAa;QACtB6B,QAAQ,EAAE,CAACV,QAAQ,IAAIzB,SAAU;QAAA2B,QAAA,EAEhC3B,SAAS,gBACNb,OAAA;UAAMiD,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAM,CAAE;UAAAZ,QAAA,gBAC/DxC,OAAA;YAAKyC,SAAS,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,gBAE3C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,GACPP,QAAQ,GAAG,kBAAkB,GAAG;MAAqB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEN7C,OAAA,CAACH,qBAAqB;MAClBwD,MAAM,EAAE5C,WAAY;MACpB6C,OAAO,EAAElB,UAAW;MACpBzB,cAAc,EAAEA;IAAe;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC,eAEF7C,OAAA,CAACF,UAAU;MACPuD,MAAM,EAAEtC,gBAAiB;MACzBuC,OAAO,EAAEjB,eAAgB;MACzBpB,YAAY,EAAEA;IAAa;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAAA,eACJ,CAAC;AAEX,CAAC;AAAArC,EAAA,CA3GYD,YAAY;EAAA,QACIZ,QAAQ;AAAA;AAAA4D,EAAA,GADxBhD,YAAY;AAAA,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}