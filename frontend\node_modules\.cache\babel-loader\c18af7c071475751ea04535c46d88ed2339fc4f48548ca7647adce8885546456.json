{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\toolbar.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// toolbar.js\n\nexport const PipelineToolbar = () => {\n  const onDragStart = (event, nodeType) => {\n    const appData = {\n      nodeType\n    };\n    event.target.style.cursor = 'grabbing';\n    event.dataTransfer.setData('application/reactflow', JSON.stringify(appData));\n    event.dataTransfer.effectAllowed = 'move';\n  };\n  const onDragEnd = event => {\n    event.target.style.cursor = 'grab';\n  };\n  const nodeTypes = [{\n    type: 'customInput',\n    label: 'Input',\n    color: '#8b5cf6'\n  }, {\n    type: 'llm',\n    label: 'LLM',\n    color: '#6366f1'\n  }, {\n    type: 'customOutput',\n    label: 'Output',\n    color: '#8b5cf6'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modern-toolbar\",\n    children: nodeTypes.map(node => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"toolbar-node\",\n      draggable: true,\n      onDragStart: event => onDragStart(event, node.type),\n      onDragEnd: onDragEnd,\n      style: {\n        backgroundColor: node.color,\n        cursor: 'grab'\n      },\n      children: node.label\n    }, node.type, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 17\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 9\n  }, this);\n};\n_c = PipelineToolbar;\nvar _c;\n$RefreshReg$(_c, \"PipelineToolbar\");", "map": {"version": 3, "names": ["PipelineToolbar", "onDragStart", "event", "nodeType", "appData", "target", "style", "cursor", "dataTransfer", "setData", "JSON", "stringify", "effectAllowed", "onDragEnd", "nodeTypes", "type", "label", "color", "_jsxDEV", "className", "children", "map", "node", "draggable", "backgroundColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/toolbar.js"], "sourcesContent": ["// toolbar.js\n\nexport const PipelineToolbar = () => {\n    const onDragStart = (event, nodeType) => {\n        const appData = { nodeType };\n        event.target.style.cursor = 'grabbing';\n        event.dataTransfer.setData('application/reactflow', JSON.stringify(appData));\n        event.dataTransfer.effectAllowed = 'move';\n    };\n\n    const onDragEnd = (event) => {\n        event.target.style.cursor = 'grab';\n    };\n\n    const nodeTypes = [\n        { type: 'customInput', label: 'Input', color: '#8b5cf6' },\n        { type: 'llm', label: 'LLM', color: '#6366f1' },\n        { type: 'customOutput', label: 'Output', color: '#8b5cf6' }\n    ];\n\n    return (\n        <div className=\"modern-toolbar\">\n            {nodeTypes.map((node) => (\n                <div\n                    key={node.type}\n                    className=\"toolbar-node\"\n                    draggable\n                    onDragStart={(event) => onDragStart(event, node.type)}\n                    onDragEnd={onDragEnd}\n                    style={{\n                        backgroundColor: node.color,\n                        cursor: 'grab'\n                    }}\n                >\n                    {node.label}\n                </div>\n            ))}\n        </div>\n    );\n};\n"], "mappings": ";;AAAA;;AAEA,OAAO,MAAMA,eAAe,GAAGA,CAAA,KAAM;EACjC,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IACrC,MAAMC,OAAO,GAAG;MAAED;IAAS,CAAC;IAC5BD,KAAK,CAACG,MAAM,CAACC,KAAK,CAACC,MAAM,GAAG,UAAU;IACtCL,KAAK,CAACM,YAAY,CAACC,OAAO,CAAC,uBAAuB,EAAEC,IAAI,CAACC,SAAS,CAACP,OAAO,CAAC,CAAC;IAC5EF,KAAK,CAACM,YAAY,CAACI,aAAa,GAAG,MAAM;EAC7C,CAAC;EAED,MAAMC,SAAS,GAAIX,KAAK,IAAK;IACzBA,KAAK,CAACG,MAAM,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;EACtC,CAAC;EAED,MAAMO,SAAS,GAAG,CACd;IAAEC,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAU,CAAC,EACzD;IAAEF,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC/C;IAAEF,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,CAC9D;EAED,oBACIC,OAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,EAC1BN,SAAS,CAACO,GAAG,CAAEC,IAAI,iBAChBJ,OAAA;MAEIC,SAAS,EAAC,cAAc;MACxBI,SAAS;MACTtB,WAAW,EAAGC,KAAK,IAAKD,WAAW,CAACC,KAAK,EAAEoB,IAAI,CAACP,IAAI,CAAE;MACtDF,SAAS,EAAEA,SAAU;MACrBP,KAAK,EAAE;QACHkB,eAAe,EAAEF,IAAI,CAACL,KAAK;QAC3BV,MAAM,EAAE;MACZ,CAAE;MAAAa,QAAA,EAEDE,IAAI,CAACN;IAAK,GAVNM,IAAI,CAACP,IAAI;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAWb,CACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAACC,EAAA,GArCW7B,eAAe;AAAA,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}