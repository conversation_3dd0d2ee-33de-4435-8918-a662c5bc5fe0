{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\App.js\";\nimport { PipelineToolbar } from './toolbar';\nimport { PipelineUI } from './ui';\nimport { SubmitButton } from './submit';\n\n// Debug logging for image/resource loading issues\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconsole.log('App.js loaded successfully');\n\n// Check if fonts are loading\ndocument.fonts.ready.then(() => {\n  console.log('Fonts loaded successfully');\n\n  // Check specifically for Studio Feixen Sans\n  const studioFeixenFont = new FontFace('Studio Feixen Sans', 'url(./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Regular.ttf)');\n  studioFeixenFont.load().then(() => {\n    console.log('✅ Studio Feixen Sans loaded successfully!');\n    document.fonts.add(studioFeixenFont);\n  }).catch(error => {\n    console.error('❌ Studio Feixen Sans loading failed:', error);\n  });\n}).catch(error => {\n  console.error('Font loading error:', error);\n});\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app-container\",\n    children: [/*#__PURE__*/_jsxDEV(PipelineToolbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PipelineUI, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SubmitButton, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["PipelineToolbar", "PipelineUI", "SubmitButton", "jsxDEV", "_jsxDEV", "console", "log", "document", "fonts", "ready", "then", "studioFeixenFont", "FontFace", "load", "add", "catch", "error", "App", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/App.js"], "sourcesContent": ["import { PipelineToolbar } from './toolbar';\nimport { PipelineUI } from './ui';\nimport { SubmitButton } from './submit';\n\n// Debug logging for image/resource loading issues\nconsole.log('App.js loaded successfully');\n\n// Check if fonts are loading\ndocument.fonts.ready.then(() => {\n  console.log('Fonts loaded successfully');\n\n  // Check specifically for Studio Feixen Sans\n  const studioFeixenFont = new FontFace('Studio Feixen Sans', 'url(./fonts/Studio Feixen Fonts TRIAL/Studio Feixen Family TRIAL/1 Studio Feixen Sans Family TRIAL/2 TTF/StudioFeixenSansTRIAL-Regular.ttf)');\n\n  studioFeixenFont.load().then(() => {\n    console.log('✅ Studio Feixen Sans loaded successfully!');\n    document.fonts.add(studioFeixenFont);\n  }).catch((error) => {\n    console.error('❌ Studio Feixen Sans loading failed:', error);\n  });\n\n}).catch((error) => {\n  console.error('Font loading error:', error);\n});\n\nfunction App() {\n  return (\n    <div className=\"app-container\">\n      <PipelineToolbar />\n      <PipelineUI />\n      <SubmitButton />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,SAASA,eAAe,QAAQ,WAAW;AAC3C,SAASC,UAAU,QAAQ,MAAM;AACjC,SAASC,YAAY,QAAQ,UAAU;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;;AAEzC;AACAC,QAAQ,CAACC,KAAK,CAACC,KAAK,CAACC,IAAI,CAAC,MAAM;EAC9BL,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;EAExC;EACA,MAAMK,gBAAgB,GAAG,IAAIC,QAAQ,CAAC,oBAAoB,EAAE,6IAA6I,CAAC;EAE1MD,gBAAgB,CAACE,IAAI,CAAC,CAAC,CAACH,IAAI,CAAC,MAAM;IACjCL,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IACxDC,QAAQ,CAACC,KAAK,CAACM,GAAG,CAACH,gBAAgB,CAAC;EACtC,CAAC,CAAC,CAACI,KAAK,CAAEC,KAAK,IAAK;IAClBX,OAAO,CAACW,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;EAC9D,CAAC,CAAC;AAEJ,CAAC,CAAC,CAACD,KAAK,CAAEC,KAAK,IAAK;EAClBX,OAAO,CAACW,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;AAC7C,CAAC,CAAC;AAEF,SAASC,GAAGA,CAAA,EAAG;EACb,oBACEb,OAAA;IAAKc,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5Bf,OAAA,CAACJ,eAAe;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnBnB,OAAA,CAACH,UAAU;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACdnB,OAAA,CAACF,YAAY;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEV;AAACC,EAAA,GARQP,GAAG;AAUZ,eAAeA,GAAG;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}