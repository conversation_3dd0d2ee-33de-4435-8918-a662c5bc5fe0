{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\inputNode.js\",\n  _s = $RefreshSig$();\n// inputNode.js\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS } from './BaseNode';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const InputNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [currName, setCurrName] = useState((data === null || data === void 0 ? void 0 : data.inputName) || id.replace('customInput-', 'input_'));\n  const [inputType, setInputType] = useState(data.inputType || 'File');\n  const [selectedFile, setSelectedFile] = useState(null);\n  const handleNameChange = e => {\n    setCurrName(e.target.value);\n  };\n  const handleTypeChange = e => {\n    setInputType(e.target.value);\n  };\n  const handleFileUpload = e => {\n    const file = e.target.files[0];\n    setSelectedFile(file);\n  };\n  const handles = [HANDLE_CONFIGS.sourceRight(`${id}-value`)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Input\",\n    handles: handles,\n    nodeType: \"input\",\n    height: 120,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"input-node-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"input-section-header\",\n          children: \"File\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-section upload-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"upload-label\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            onChange: handleFileUpload,\n            style: {\n              display: 'none'\n            },\n            accept: \"*/*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"upload-button\",\n            children: \"Upload file\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), selectedFile && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"file-info\",\n          children: selectedFile.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(InputNode, \"JQ1ibykWtSSHvmBGoROw7sNfsDU=\");\n_c = InputNode;\nvar _c;\n$RefreshReg$(_c, \"InputNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "HANDLE_CONFIGS", "jsxDEV", "_jsxDEV", "InputNode", "id", "data", "_s", "currName", "setCurrName", "inputName", "replace", "inputType", "setInputType", "selectedFile", "setSelectedFile", "handleNameChange", "e", "target", "value", "handleTypeChange", "handleFileUpload", "file", "files", "handles", "sourceRight", "title", "nodeType", "height", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onChange", "style", "display", "accept", "name", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/inputNode.js"], "sourcesContent": ["// inputNode.js\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS } from './BaseNode';\n\nexport const InputNode = ({ id, data }) => {\n  const [currName, setCurrName] = useState(data?.inputName || id.replace('customInput-', 'input_'));\n  const [inputType, setInputType] = useState(data.inputType || 'File');\n  const [selectedFile, setSelectedFile] = useState(null);\n\n  const handleNameChange = (e) => {\n    setCurrName(e.target.value);\n  };\n\n  const handleTypeChange = (e) => {\n    setInputType(e.target.value);\n  };\n\n  const handleFileUpload = (e) => {\n    const file = e.target.files[0];\n    setSelectedFile(file);\n  };\n\n  const handles = [HANDLE_CONFIGS.sourceRight(`${id}-value`)];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Input\"\n      handles={handles}\n      nodeType=\"input\"\n      height={120}\n    >\n      <div className=\"input-node-content\">\n        {/* File Section */}\n        <div className=\"input-section\">\n          <div className=\"input-section-header\">\n            File\n          </div>\n        </div>\n\n        {/* Upload File Section */}\n        <div className=\"input-section upload-section\">\n          <label className=\"upload-label\">\n            <input\n              type=\"file\"\n              onChange={handleFileUpload}\n              style={{ display: 'none' }}\n              accept=\"*/*\"\n            />\n            <div className=\"upload-button\">\n              Upload file\n            </div>\n          </label>\n          {selectedFile && (\n            <div className=\"file-info\">\n              {selectedFile.name}\n            </div>\n          )}\n        </div>\n      </div>\n    </BaseNode>\n  );\n}\n"], "mappings": ";;AAAA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,cAAc,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,OAAO,MAAMC,SAAS,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,CAAAO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,SAAS,KAAIL,EAAE,CAACM,OAAO,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;EACjG,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAACO,IAAI,CAACM,SAAS,IAAI,MAAM,CAAC;EACpE,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMiB,gBAAgB,GAAIC,CAAC,IAAK;IAC9BR,WAAW,CAACQ,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMC,gBAAgB,GAAIH,CAAC,IAAK;IAC9BJ,YAAY,CAACI,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAME,gBAAgB,GAAIJ,CAAC,IAAK;IAC9B,MAAMK,IAAI,GAAGL,CAAC,CAACC,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC;IAC9BR,eAAe,CAACO,IAAI,CAAC;EACvB,CAAC;EAED,MAAME,OAAO,GAAG,CAACvB,cAAc,CAACwB,WAAW,CAAE,GAAEpB,EAAG,QAAO,CAAC,CAAC;EAE3D,oBACEF,OAAA,CAACH,QAAQ;IACPK,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXoB,KAAK,EAAC,OAAO;IACbF,OAAO,EAAEA,OAAQ;IACjBG,QAAQ,EAAC,OAAO;IAChBC,MAAM,EAAE,GAAI;IAAAC,QAAA,eAEZ1B,OAAA;MAAK2B,SAAS,EAAC,oBAAoB;MAAAD,QAAA,gBAEjC1B,OAAA;QAAK2B,SAAS,EAAC,eAAe;QAAAD,QAAA,eAC5B1B,OAAA;UAAK2B,SAAS,EAAC,sBAAsB;UAAAD,QAAA,EAAC;QAEtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/B,OAAA;QAAK2B,SAAS,EAAC,8BAA8B;QAAAD,QAAA,gBAC3C1B,OAAA;UAAO2B,SAAS,EAAC,cAAc;UAAAD,QAAA,gBAC7B1B,OAAA;YACEgC,IAAI,EAAC,MAAM;YACXC,QAAQ,EAAEf,gBAAiB;YAC3BgB,KAAK,EAAE;cAAEC,OAAO,EAAE;YAAO,CAAE;YAC3BC,MAAM,EAAC;UAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACF/B,OAAA;YAAK2B,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAE/B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACPpB,YAAY,iBACXX,OAAA;UAAK2B,SAAS,EAAC,WAAW;UAAAD,QAAA,EACvBf,YAAY,CAAC0B;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAA3B,EAAA,CA3DYH,SAAS;AAAAqC,EAAA,GAATrC,SAAS;AAAA,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}