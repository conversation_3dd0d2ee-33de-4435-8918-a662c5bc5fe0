// toolbar.js

export const PipelineToolbar = () => {
    const onDragStart = (event, nodeType) => {
        const appData = { nodeType };
        event.target.style.cursor = 'grabbing';
        event.dataTransfer.setData('application/reactflow', JSON.stringify(appData));
        event.dataTransfer.effectAllowed = 'move';
    };

    const onDragEnd = (event) => {
        event.target.style.cursor = 'grab';
    };

    const nodeTypes = [
        { type: 'customInput', label: 'Input', color: '#8b5cf6' },
        { type: 'llm', label: 'LLM', color: '#6366f1' },
        { type: 'customOutput', label: 'Output', color: '#8b5cf6' }
    ];

    return (
        <div className="modern-toolbar">
            {nodeTypes.map((node) => (
                <div
                    key={node.type}
                    className="toolbar-node"
                    draggable
                    onDragStart={(event) => onDragStart(event, node.type)}
                    onDragEnd={onDragEnd}
                    style={{
                        backgroundColor: node.color,
                        cursor: 'grab'
                    }}
                >
                    {node.label}
                </div>
            ))}
        </div>
    );
};
