{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\inputNode.js\",\n  _s = $RefreshSig$();\n// inputNode.js\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS, commonLabelStyle } from './BaseNode';\nimport { NodeInput } from '../components/NodeInput';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const InputNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [currName, setCurrName] = useState((data === null || data === void 0 ? void 0 : data.inputName) || id.replace('customInput-', 'input_'));\n  const [inputType, setInputType] = useState(data.inputType || 'Text');\n  const handleNameChange = e => {\n    setCurrName(e.target.value);\n  };\n  const handleTypeChange = e => {\n    setInputType(e.target.value);\n  };\n  const handles = [HANDLE_CONFIGS.sourceRight('value')];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Input\",\n    handles: handles,\n    nodeType: \"input\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: commonLabelStyle,\n        children: [\"Name:\", /*#__PURE__*/_jsxDEV(NodeInput, {\n          value: currName,\n          onChange: handleNameChange,\n          placeholder: \"Enter input name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        style: commonLabelStyle,\n        children: [\"Type:\", /*#__PURE__*/_jsxDEV(\"select\", {\n          value: inputType,\n          onChange: handleTypeChange,\n          className: \"node-input\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Text\",\n            children: \"Text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"File\",\n            children: \"File\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(InputNode, \"K/fVZ6JMb9mTcAC2LWPy4yI2M3A=\");\n_c = InputNode;\nvar _c;\n$RefreshReg$(_c, \"InputNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "HANDLE_CONFIGS", "commonLabelStyle", "NodeInput", "jsxDEV", "_jsxDEV", "InputNode", "id", "data", "_s", "currName", "setCurrName", "inputName", "replace", "inputType", "setInputType", "handleNameChange", "e", "target", "value", "handleTypeChange", "handles", "sourceRight", "title", "nodeType", "children", "style", "display", "flexDirection", "gap", "onChange", "placeholder", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "cursor", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/inputNode.js"], "sourcesContent": ["// inputNode.js\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS, commonLabelStyle } from './BaseNode';\nimport { NodeInput } from '../components/NodeInput';\n\nexport const InputNode = ({ id, data }) => {\n  const [currName, setCurrName] = useState(data?.inputName || id.replace('customInput-', 'input_'));\n  const [inputType, setInputType] = useState(data.inputType || 'Text');\n\n  const handleNameChange = (e) => {\n    setCurrName(e.target.value);\n  };\n\n  const handleTypeChange = (e) => {\n    setInputType(e.target.value);\n  };\n\n  const handles = [HANDLE_CONFIGS.sourceRight('value')];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Input\"\n      handles={handles}\n      nodeType=\"input\"\n    >\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n        <label style={commonLabelStyle}>\n          Name:\n          <NodeInput\n            value={currName}\n            onChange={handleNameChange}\n            placeholder=\"Enter input name\"\n          />\n        </label>\n        <label style={commonLabelStyle}>\n          Type:\n          <select\n            value={inputType}\n            onChange={handleTypeChange}\n            className=\"node-input\"\n            style={{ cursor: 'pointer' }}\n          >\n            <option value=\"Text\">Text</option>\n            <option value=\"File\">File</option>\n          </select>\n        </label>\n      </div>\n    </BaseNode>\n  );\n}\n"], "mappings": ";;AAAA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,YAAY;AACvE,SAASC,SAAS,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,OAAO,MAAMC,SAAS,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,SAAS,KAAIL,EAAE,CAACM,OAAO,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;EACjG,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAACS,IAAI,CAACM,SAAS,IAAI,MAAM,CAAC;EAEpE,MAAME,gBAAgB,GAAIC,CAAC,IAAK;IAC9BN,WAAW,CAACM,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMC,gBAAgB,GAAIH,CAAC,IAAK;IAC9BF,YAAY,CAACE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAME,OAAO,GAAG,CAACpB,cAAc,CAACqB,WAAW,CAAC,OAAO,CAAC,CAAC;EAErD,oBACEjB,OAAA,CAACL,QAAQ;IACPO,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXe,KAAK,EAAC,OAAO;IACbF,OAAO,EAAEA,OAAQ;IACjBG,QAAQ,EAAC,OAAO;IAAAC,QAAA,eAEhBpB,OAAA;MAAKqB,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAM,CAAE;MAAAJ,QAAA,gBACnEpB,OAAA;QAAOqB,KAAK,EAAExB,gBAAiB;QAAAuB,QAAA,GAAC,OAE9B,eAAApB,OAAA,CAACF,SAAS;UACRgB,KAAK,EAAET,QAAS;UAChBoB,QAAQ,EAAEd,gBAAiB;UAC3Be,WAAW,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACR9B,OAAA;QAAOqB,KAAK,EAAExB,gBAAiB;QAAAuB,QAAA,GAAC,OAE9B,eAAApB,OAAA;UACEc,KAAK,EAAEL,SAAU;UACjBgB,QAAQ,EAAEV,gBAAiB;UAC3BgB,SAAS,EAAC,YAAY;UACtBV,KAAK,EAAE;YAAEW,MAAM,EAAE;UAAU,CAAE;UAAAZ,QAAA,gBAE7BpB,OAAA;YAAQc,KAAK,EAAC,MAAM;YAAAM,QAAA,EAAC;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClC9B,OAAA;YAAQc,KAAK,EAAC,MAAM;YAAAM,QAAA,EAAC;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAA1B,EAAA,CA9CYH,SAAS;AAAAgC,EAAA,GAAThC,SAAS;AAAA,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}