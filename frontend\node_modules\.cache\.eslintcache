[{"D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\index.js": "1", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\App.js": "2", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\toolbar.js": "3", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\ui.js": "4", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\submit.js": "5", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\draggableNode.js": "6", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\store.js": "7", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\inputNode.js": "8", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\outputNode.js": "9", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\textNode.js": "10", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\llmNode.js": "11", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\BaseNode.js": "12", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\switchNode.js": "13", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\mathNode.js": "14", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\filterNode.js": "15", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\aggregatorNode.js": "16", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\timerNode.js": "17", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\styles\\theme.js": "18", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\NodeInput.js": "19", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\Modal.js": "20"}, {"size": 254, "mtime": 1750274986338, "results": "21", "hashOfConfig": "22"}, {"size": 572, "mtime": 1750354407260, "results": "23", "hashOfConfig": "22"}, {"size": 2770, "mtime": 1750355322634, "results": "24", "hashOfConfig": "22"}, {"size": 4875, "mtime": 1750355314208, "results": "25", "hashOfConfig": "22"}, {"size": 4136, "mtime": 1750314956052, "results": "26", "hashOfConfig": "22"}, {"size": 621, "mtime": 1750285171720, "results": "27", "hashOfConfig": "22"}, {"size": 1353, "mtime": 1750285195771, "results": "28", "hashOfConfig": "22"}, {"size": 1440, "mtime": 1750352626170, "results": "29", "hashOfConfig": "22"}, {"size": 1453, "mtime": 1750354081195, "results": "30", "hashOfConfig": "22"}, {"size": 4591, "mtime": 1750354407256, "results": "31", "hashOfConfig": "22"}, {"size": 762, "mtime": 1750352626197, "results": "32", "hashOfConfig": "22"}, {"size": 4267, "mtime": 1750354407251, "results": "33", "hashOfConfig": "22"}, {"size": 1357, "mtime": 1750315019076, "results": "34", "hashOfConfig": "22"}, {"size": 1367, "mtime": 1750352626197, "results": "35", "hashOfConfig": "22"}, {"size": 1564, "mtime": 1750315007000, "results": "36", "hashOfConfig": "22"}, {"size": 2110, "mtime": 1750314994727, "results": "37", "hashOfConfig": "22"}, {"size": 1396, "mtime": 1750315076112, "results": "38", "hashOfConfig": "22"}, {"size": 2138, "mtime": 1750279121739, "results": "39", "hashOfConfig": "22"}, {"size": 718, "mtime": 1750286504985, "results": "40", "hashOfConfig": "22"}, {"size": 3508, "mtime": 1750314892016, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, "159wf7k", {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "61"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\index.js", [], [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\App.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\toolbar.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\ui.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\submit.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\draggableNode.js", [], [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\store.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\inputNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\outputNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\textNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\llmNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\BaseNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\switchNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\mathNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\filterNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\aggregatorNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\timerNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\styles\\theme.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\NodeInput.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\Modal.js", [], []]