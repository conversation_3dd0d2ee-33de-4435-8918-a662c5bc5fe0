{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\toolbar.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// toolbar.js\n\nexport const PipelineToolbar = () => {\n  const onDragStart = (event, nodeType) => {\n    const appData = {\n      nodeType\n    };\n    event.target.style.cursor = 'grabbing';\n    event.dataTransfer.setData('application/reactflow', JSON.stringify(appData));\n    event.dataTransfer.effectAllowed = 'move';\n  };\n  const onDragEnd = event => {\n    event.target.style.cursor = 'grab';\n  };\n  const nodeTypes = [{\n    type: 'customInput',\n    label: 'Input',\n    color: '#8b5cf6'\n  }, {\n    type: 'llm',\n    label: 'LLM',\n    color: '#6366f1'\n  }, {\n    type: 'customOutput',\n    label: 'Output',\n    color: '#8b5cf6'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pipeline-toolbar\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"toolbar-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-brand\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"brand-logo\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"logo-svg\",\n            width: \"32\",\n            height: \"32\",\n            viewBox: \"0 0 32 32\",\n            fill: \"none\",\n            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M16 2L30 9v14L16 30 2 23V9L16 2z\",\n              fill: \"url(#gradient)\",\n              stroke: \"#8b5cf6\",\n              strokeWidth: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"defs\", {\n              children: /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n                id: \"gradient\",\n                x1: \"0%\",\n                y1: \"0%\",\n                x2: \"100%\",\n                y2: \"100%\",\n                children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n                  offset: \"0%\",\n                  stopColor: \"#8b5cf6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 30,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n                  offset: \"100%\",\n                  stopColor: \"#6366f1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 31,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"brand-text\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"toolbar-title\",\n            children: \"VectorShift Pipeline Builder\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-nodes\",\n        children: nodeTypes.map(node => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"draggable-node\",\n          draggable: true,\n          onDragStart: event => onDragStart(event, node.type),\n          onDragEnd: onDragEnd,\n          style: {\n            cursor: 'grab'\n          },\n          children: node.label\n        }, node.type, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 9\n  }, this);\n};\n_c = PipelineToolbar;\nvar _c;\n$RefreshReg$(_c, \"PipelineToolbar\");", "map": {"version": 3, "names": ["PipelineToolbar", "onDragStart", "event", "nodeType", "appData", "target", "style", "cursor", "dataTransfer", "setData", "JSON", "stringify", "effectAllowed", "onDragEnd", "nodeTypes", "type", "label", "color", "_jsxDEV", "className", "children", "width", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "x1", "y1", "x2", "y2", "offset", "stopColor", "map", "node", "draggable", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/toolbar.js"], "sourcesContent": ["// toolbar.js\n\nexport const PipelineToolbar = () => {\n    const onDragStart = (event, nodeType) => {\n        const appData = { nodeType };\n        event.target.style.cursor = 'grabbing';\n        event.dataTransfer.setData('application/reactflow', JSON.stringify(appData));\n        event.dataTransfer.effectAllowed = 'move';\n    };\n\n    const onDragEnd = (event) => {\n        event.target.style.cursor = 'grab';\n    };\n\n    const nodeTypes = [\n        { type: 'customInput', label: 'Input', color: '#8b5cf6' },\n        { type: 'llm', label: 'LLM', color: '#6366f1' },\n        { type: 'customOutput', label: 'Output', color: '#8b5cf6' }\n    ];\n\n    return (\n        <div className=\"pipeline-toolbar\">\n            <div className=\"toolbar-header\">\n                <div className=\"toolbar-brand\">\n                    <div className=\"brand-logo\">\n                        <svg className=\"logo-svg\" width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\">\n                            <path d=\"M16 2L30 9v14L16 30 2 23V9L16 2z\" fill=\"url(#gradient)\" stroke=\"#8b5cf6\" strokeWidth=\"1\"/>\n                            <defs>\n                                <linearGradient id=\"gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                                    <stop offset=\"0%\" stopColor=\"#8b5cf6\"/>\n                                    <stop offset=\"100%\" stopColor=\"#6366f1\"/>\n                                </linearGradient>\n                            </defs>\n                        </svg>\n                    </div>\n                    <div className=\"brand-text\">\n                        <div className=\"toolbar-title\">VectorShift Pipeline Builder</div>\n                    </div>\n                </div>\n                <div className=\"toolbar-nodes\">\n                    {nodeTypes.map((node) => (\n                        <div\n                            key={node.type}\n                            className=\"draggable-node\"\n                            draggable\n                            onDragStart={(event) => onDragStart(event, node.type)}\n                            onDragEnd={onDragEnd}\n                            style={{\n                                cursor: 'grab'\n                            }}\n                        >\n                            {node.label}\n                        </div>\n                    ))}\n                </div>\n            </div>\n        </div>\n    );\n};\n"], "mappings": ";;AAAA;;AAEA,OAAO,MAAMA,eAAe,GAAGA,CAAA,KAAM;EACjC,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IACrC,MAAMC,OAAO,GAAG;MAAED;IAAS,CAAC;IAC5BD,KAAK,CAACG,MAAM,CAACC,KAAK,CAACC,MAAM,GAAG,UAAU;IACtCL,KAAK,CAACM,YAAY,CAACC,OAAO,CAAC,uBAAuB,EAAEC,IAAI,CAACC,SAAS,CAACP,OAAO,CAAC,CAAC;IAC5EF,KAAK,CAACM,YAAY,CAACI,aAAa,GAAG,MAAM;EAC7C,CAAC;EAED,MAAMC,SAAS,GAAIX,KAAK,IAAK;IACzBA,KAAK,CAACG,MAAM,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;EACtC,CAAC;EAED,MAAMO,SAAS,GAAG,CACd;IAAEC,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAU,CAAC,EACzD;IAAEF,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC/C;IAAEF,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,CAC9D;EAED,oBACIC,OAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAC7BF,OAAA;MAAKC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC3BF,OAAA;QAAKC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1BF,OAAA;UAAKC,SAAS,EAAC,YAAY;UAAAC,QAAA,eACvBF,OAAA;YAAKC,SAAS,EAAC,UAAU;YAACE,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAAAJ,QAAA,gBAC5EF,OAAA;cAAMO,CAAC,EAAC,kCAAkC;cAACD,IAAI,EAAC,gBAAgB;cAACE,MAAM,EAAC,SAAS;cAACC,WAAW,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eACnGb,OAAA;cAAAE,QAAA,eACIF,OAAA;gBAAgBc,EAAE,EAAC,UAAU;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,MAAM;gBAACC,EAAE,EAAC,MAAM;gBAAAhB,QAAA,gBAC7DF,OAAA;kBAAMmB,MAAM,EAAC,IAAI;kBAACC,SAAS,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACvCb,OAAA;kBAAMmB,MAAM,EAAC,MAAM;kBAACC,SAAS,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNb,OAAA;UAAKC,SAAS,EAAC,YAAY;UAAAC,QAAA,eACvBF,OAAA;YAAKC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA4B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNb,OAAA;QAAKC,SAAS,EAAC,eAAe;QAAAC,QAAA,EACzBN,SAAS,CAACyB,GAAG,CAAEC,IAAI,iBAChBtB,OAAA;UAEIC,SAAS,EAAC,gBAAgB;UAC1BsB,SAAS;UACTxC,WAAW,EAAGC,KAAK,IAAKD,WAAW,CAACC,KAAK,EAAEsC,IAAI,CAACzB,IAAI,CAAE;UACtDF,SAAS,EAAEA,SAAU;UACrBP,KAAK,EAAE;YACHC,MAAM,EAAE;UACZ,CAAE;UAAAa,QAAA,EAEDoB,IAAI,CAACxB;QAAK,GATNwB,IAAI,CAACzB,IAAI;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUb,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACW,EAAA,GAxDW1C,eAAe;AAAA,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}