{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\outputNode.js\",\n  _s = $RefreshSig$();\n// outputNode.js\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS, commonLabelStyle } from './BaseNode';\nimport { NodeInput } from '../components/NodeInput';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const OutputNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [currName, setCurrName] = useState((data === null || data === void 0 ? void 0 : data.outputName) || id.replace('customOutput-', 'output_'));\n  const [outputType, setOutputType] = useState(data.outputType || 'Text');\n  const handleNameChange = e => {\n    setCurrName(e.target.value);\n  };\n  const handleTypeChange = e => {\n    setOutputType(e.target.value);\n  };\n  const handles = [HANDLE_CONFIGS.targetLeft('value')];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Output\",\n    handles: handles,\n    nodeType: \"output\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          fontSize: '12px',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '4px',\n          color: '#ffffff',\n          fontWeight: '600'\n        },\n        children: [\"Name:\", /*#__PURE__*/_jsxDEV(NodeInput, {\n          value: currName,\n          onChange: handleNameChange,\n          placeholder: \"Enter output name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          fontSize: '12px',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '4px',\n          color: '#ffffff',\n          fontWeight: '600'\n        },\n        children: [\"Type:\", /*#__PURE__*/_jsxDEV(\"select\", {\n          value: outputType,\n          onChange: handleTypeChange,\n          className: \"node-input\",\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Text\",\n            children: \"Text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"File\",\n            children: \"Image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(OutputNode, \"B7OtEwNf7ml8MnkX7q6OcbfpAhQ=\");\n_c = OutputNode;\nvar _c;\n$RefreshReg$(_c, \"OutputNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "HANDLE_CONFIGS", "commonLabelStyle", "NodeInput", "jsxDEV", "_jsxDEV", "OutputNode", "id", "data", "_s", "currName", "setCurrName", "outputName", "replace", "outputType", "setOutputType", "handleNameChange", "e", "target", "value", "handleTypeChange", "handles", "targetLeft", "title", "nodeType", "children", "style", "display", "flexDirection", "gap", "fontSize", "color", "fontWeight", "onChange", "placeholder", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "cursor", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/outputNode.js"], "sourcesContent": ["// outputNode.js\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS, commonLabelStyle } from './BaseNode';\nimport { NodeInput } from '../components/NodeInput';\n\nexport const OutputNode = ({ id, data }) => {\n  const [currName, setCurrName] = useState(data?.outputName || id.replace('customOutput-', 'output_'));\n  const [outputType, setOutputType] = useState(data.outputType || 'Text');\n\n  const handleNameChange = (e) => {\n    setCurrName(e.target.value);\n  };\n\n  const handleTypeChange = (e) => {\n    setOutputType(e.target.value);\n  };\n\n  const handles = [HANDLE_CONFIGS.targetLeft('value')];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Output\"\n      handles={handles}\n      nodeType=\"output\"\n    >\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n        <label style={{\n          fontSize: '12px',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '4px',\n          color: '#ffffff',\n          fontWeight: '600'\n        }}>\n          Name:\n          <NodeInput\n            value={currName}\n            onChange={handleNameChange}\n            placeholder=\"Enter output name\"\n          />\n        </label>\n        <label style={{\n          fontSize: '12px',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '4px',\n          color: '#ffffff',\n          fontWeight: '600'\n        }}>\n          Type:\n          <select\n            value={outputType}\n            onChange={handleTypeChange}\n            className=\"node-input\"\n            style={{ cursor: 'pointer' }}\n          >\n            <option value=\"Text\">Text</option>\n            <option value=\"File\">Image</option>\n          </select>\n        </label>\n      </div>\n    </BaseNode>\n  );\n}\n"], "mappings": ";;AAAA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,YAAY;AACvE,SAASC,SAAS,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,OAAO,MAAMC,UAAU,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC1C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,UAAU,KAAIL,EAAE,CAACM,OAAO,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;EACpG,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAACS,IAAI,CAACM,UAAU,IAAI,MAAM,CAAC;EAEvE,MAAME,gBAAgB,GAAIC,CAAC,IAAK;IAC9BN,WAAW,CAACM,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMC,gBAAgB,GAAIH,CAAC,IAAK;IAC9BF,aAAa,CAACE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAME,OAAO,GAAG,CAACpB,cAAc,CAACqB,UAAU,CAAC,OAAO,CAAC,CAAC;EAEpD,oBACEjB,OAAA,CAACL,QAAQ;IACPO,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXe,KAAK,EAAC,QAAQ;IACdF,OAAO,EAAEA,OAAQ;IACjBG,QAAQ,EAAC,QAAQ;IAAAC,QAAA,eAEjBpB,OAAA;MAAKqB,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAM,CAAE;MAAAJ,QAAA,gBACnEpB,OAAA;QAAOqB,KAAK,EAAE;UACZI,QAAQ,EAAE,MAAM;UAChBH,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,GAAG,EAAE,KAAK;UACVE,KAAK,EAAE,SAAS;UAChBC,UAAU,EAAE;QACd,CAAE;QAAAP,QAAA,GAAC,OAED,eAAApB,OAAA,CAACF,SAAS;UACRgB,KAAK,EAAET,QAAS;UAChBuB,QAAQ,EAAEjB,gBAAiB;UAC3BkB,WAAW,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACRjC,OAAA;QAAOqB,KAAK,EAAE;UACZI,QAAQ,EAAE,MAAM;UAChBH,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,GAAG,EAAE,KAAK;UACVE,KAAK,EAAE,SAAS;UAChBC,UAAU,EAAE;QACd,CAAE;QAAAP,QAAA,GAAC,OAED,eAAApB,OAAA;UACEc,KAAK,EAAEL,UAAW;UAClBmB,QAAQ,EAAEb,gBAAiB;UAC3BmB,SAAS,EAAC,YAAY;UACtBb,KAAK,EAAE;YAAEc,MAAM,EAAE;UAAU,CAAE;UAAAf,QAAA,gBAE7BpB,OAAA;YAAQc,KAAK,EAAC,MAAM;YAAAM,QAAA,EAAC;UAAI;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClCjC,OAAA;YAAQc,KAAK,EAAC,MAAM;YAAAM,QAAA,EAAC;UAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAA7B,EAAA,CA5DYH,UAAU;AAAAmC,EAAA,GAAVnC,UAAU;AAAA,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}