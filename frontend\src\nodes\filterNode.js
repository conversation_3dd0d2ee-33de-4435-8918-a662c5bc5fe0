// filterNode.js
// Demonstrates data filtering with configurable conditions

import { useState } from 'react';
import { BaseNode, createHandle } from './BaseNode';
import { Position } from 'reactflow';

export const FilterNode = ({ id, data }) => {
  const [condition, setCondition] = useState(data?.condition || 'contains');
  const [value, setValue] = useState(data?.value || '');

  const handleConditionChange = (e) => {
    setCondition(e.target.value);
  };

  const handleValueChange = (e) => {
    setValue(e.target.value);
  };

  const handles = [
    createHandle(`${id}-input`, 'target', Position.Left),
    createHandle(`${id}-filtered`, 'source', Position.Right, { top: '30%' }),
    createHandle(`${id}-rejected`, 'source', Position.Right, { top: '70%' })
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Filter"
      handles={handles}
      nodeType="filter"
      height={100}
    >
      <div>
        <label>
          Condition:
          <select
            value={condition}
            onChange={handleConditionChange}
            className="node-input"
          >
            <option value="contains">Contains</option>
            <option value="equals">Equals</option>
            <option value="startsWith">Starts with</option>
          </select>
        </label>
        <label>
          Value:
          <input
            type="text"
            value={value}
            onChange={handleValueChange}
            className="node-input"
          />
        </label>
      </div>
    </BaseNode>
  );
};
