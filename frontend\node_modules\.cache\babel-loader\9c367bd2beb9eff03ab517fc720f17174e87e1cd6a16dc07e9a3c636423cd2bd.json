{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\BaseNode.js\";\n// BaseNode.js\n// Reusable base component for all node types\n\nimport { Handle, Position } from 'reactflow';\nimport { theme } from '../styles/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const BaseNode = ({\n  id,\n  data,\n  title,\n  children,\n  handles = [],\n  style = {},\n  className = \"\",\n  width = 140,\n  height = 70,\n  nodeType = 'default'\n}) => {\n  const defaultStyle = {\n    width,\n    minHeight: height,\n    background: 'linear-gradient(135deg, rgba(26, 11, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%)',\n    border: '1px solid rgba(138, 43, 226, 0.6)',\n    borderRadius: '16px',\n    padding: theme.spacing.sm,\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'flex-start',\n    alignItems: 'stretch',\n    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n    fontFamily: \"'Studio Feixen Sans', 'Inter', sans-serif\",\n    position: 'relative',\n    color: '#ffffff',\n    backdropFilter: 'blur(20px)',\n    boxShadow: '0 8px 32px rgba(138, 43, 226, 0.3)',\n    boxSizing: 'border-box',\n    ...style\n  };\n  const titleStyle = {\n    fontWeight: '600',\n    fontSize: '10px',\n    color: '#ffffff',\n    marginBottom: '3px',\n    textAlign: 'center',\n    borderBottom: '1px solid rgba(138, 43, 226, 0.3)',\n    paddingBottom: '3px',\n    textTransform: 'uppercase',\n    letterSpacing: '0.5px',\n    background: 'linear-gradient(90deg, #8b5cf6, #a855f7)',\n    backgroundClip: 'text',\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent'\n  };\n  const contentStyle = {\n    flex: 1,\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'flex-start',\n    fontSize: theme.typography.fontSize.xs,\n    gap: theme.spacing.sm,\n    paddingTop: theme.spacing.xs\n  };\n  const handleStyle = {\n    width: '12px',\n    height: '12px',\n    background: 'linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%)',\n    border: '2px solid #ffffff',\n    borderRadius: '50%',\n    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n    boxShadow: '0 2px 8px rgba(138, 43, 226, 0.4)'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: defaultStyle,\n    className: `base-node ${className}`,\n    onMouseEnter: e => {\n      e.currentTarget.style.transform = 'translateY(-2px) scale(1.02)';\n      e.currentTarget.style.borderColor = 'rgba(138, 43, 226, 0.8)';\n      e.currentTarget.style.background = 'linear-gradient(135deg, rgba(26, 11, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%)';\n      e.currentTarget.style.boxShadow = '0 12px 40px rgba(138, 43, 226, 0.3)';\n    },\n    onMouseLeave: e => {\n      e.currentTarget.style.transform = 'translateY(0) scale(1)';\n      e.currentTarget.style.borderColor = 'rgba(138, 43, 226, 0.4)';\n      e.currentTarget.style.background = 'linear-gradient(135deg, rgba(26, 11, 46, 0.9) 0%, rgba(22, 33, 62, 0.9) 100%)';\n      e.currentTarget.style.boxShadow = '0 8px 32px rgba(138, 43, 226, 0.2)';\n    },\n    children: [handles.map((handle, index) => /*#__PURE__*/_jsxDEV(Handle, {\n      type: handle.type,\n      position: handle.position,\n      id: `${id}-${handle.id}`,\n      style: {\n        ...handleStyle,\n        ...handle.style\n      }\n    }, `${id}-${handle.id || index}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 9\n    }, this)), title && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: titleStyle,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: contentStyle,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n};\n\n// Helper function to create handle configurations\n_c = BaseNode;\nexport const createHandle = (id, type, position, style = {}) => ({\n  id,\n  type,\n  position,\n  style\n});\n\n// Common handle configurations\nexport const HANDLE_CONFIGS = {\n  sourceRight: (handleId = 'output') => createHandle(handleId, 'source', Position.Right),\n  targetLeft: (handleId = 'input') => createHandle(handleId, 'target', Position.Left),\n  targetTop: (handleId = 'input') => createHandle(handleId, 'target', Position.Top),\n  sourceBottom: (handleId = 'output') => createHandle(handleId, 'source', Position.Bottom)\n};\n\n// Common label styling for form inputs - extracted to maintain consistency\nexport const commonLabelStyle = {\n  fontSize: '12px',\n  display: 'flex',\n  flexDirection: 'column',\n  gap: '4px',\n  color: '#ffffff',\n  fontWeight: '600'\n};\nvar _c;\n$RefreshReg$(_c, \"BaseNode\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Position", "theme", "jsxDEV", "_jsxDEV", "BaseNode", "id", "data", "title", "children", "handles", "style", "className", "width", "height", "nodeType", "defaultStyle", "minHeight", "background", "border", "borderRadius", "padding", "spacing", "sm", "display", "flexDirection", "justifyContent", "alignItems", "transition", "fontFamily", "position", "color", "<PERSON><PERSON>ilter", "boxShadow", "boxSizing", "titleStyle", "fontWeight", "fontSize", "marginBottom", "textAlign", "borderBottom", "paddingBottom", "textTransform", "letterSpacing", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "contentStyle", "flex", "typography", "xs", "gap", "paddingTop", "handleStyle", "onMouseEnter", "e", "currentTarget", "transform", "borderColor", "onMouseLeave", "map", "handle", "index", "type", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "createHandle", "HANDLE_CONFIGS", "sourceRight", "handleId", "Right", "targetLeft", "Left", "targetTop", "Top", "sourceBottom", "Bottom", "commonLabelStyle", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/BaseNode.js"], "sourcesContent": ["// BaseNode.js\n// Reusable base component for all node types\n\nimport { Handle, Position } from 'reactflow';\nimport { theme } from '../styles/theme';\n\nexport const BaseNode = ({\n  id,\n  data,\n  title,\n  children,\n  handles = [],\n  style = {},\n  className = \"\",\n  width = 140,\n  height = 70,\n  nodeType = 'default'\n}) => {\n\n\n  const defaultStyle = {\n    width,\n    minHeight: height,\n    background: 'linear-gradient(135deg, rgba(26, 11, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%)',\n    border: '1px solid rgba(138, 43, 226, 0.6)',\n    borderRadius: '16px',\n    padding: theme.spacing.sm,\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'flex-start',\n    alignItems: 'stretch',\n    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n    fontFamily: \"'Studio Feixen Sans', 'Inter', sans-serif\",\n    position: 'relative',\n    color: '#ffffff',\n    backdropFilter: 'blur(20px)',\n    boxShadow: '0 8px 32px rgba(138, 43, 226, 0.3)',\n    boxSizing: 'border-box',\n    ...style\n  };\n\n  const titleStyle = {\n    fontWeight: '600',\n    fontSize: '10px',\n    color: '#ffffff',\n    marginBottom: '3px',\n    textAlign: 'center',\n    borderBottom: '1px solid rgba(138, 43, 226, 0.3)',\n    paddingBottom: '3px',\n    textTransform: 'uppercase',\n    letterSpacing: '0.5px',\n    background: 'linear-gradient(90deg, #8b5cf6, #a855f7)',\n    backgroundClip: 'text',\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent',\n  };\n\n  const contentStyle = {\n    flex: 1,\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'flex-start',\n    fontSize: theme.typography.fontSize.xs,\n    gap: theme.spacing.sm,\n    paddingTop: theme.spacing.xs,\n  };\n\n  const handleStyle = {\n    width: '12px',\n    height: '12px',\n    background: 'linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%)',\n    border: '2px solid #ffffff',\n    borderRadius: '50%',\n    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',\n    boxShadow: '0 2px 8px rgba(138, 43, 226, 0.4)',\n  };\n\n  return (\n    <div\n      style={defaultStyle}\n      className={`base-node ${className}`}\n      onMouseEnter={(e) => {\n        e.currentTarget.style.transform = 'translateY(-2px) scale(1.02)';\n        e.currentTarget.style.borderColor = 'rgba(138, 43, 226, 0.8)';\n        e.currentTarget.style.background = 'linear-gradient(135deg, rgba(26, 11, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%)';\n        e.currentTarget.style.boxShadow = '0 12px 40px rgba(138, 43, 226, 0.3)';\n      }}\n      onMouseLeave={(e) => {\n        e.currentTarget.style.transform = 'translateY(0) scale(1)';\n        e.currentTarget.style.borderColor = 'rgba(138, 43, 226, 0.4)';\n        e.currentTarget.style.background = 'linear-gradient(135deg, rgba(26, 11, 46, 0.9) 0%, rgba(22, 33, 62, 0.9) 100%)';\n        e.currentTarget.style.boxShadow = '0 8px 32px rgba(138, 43, 226, 0.2)';\n      }}\n    >\n      {/* Render handles */}\n      {handles.map((handle, index) => (\n        <Handle\n          key={`${id}-${handle.id || index}`}\n          type={handle.type}\n          position={handle.position}\n          id={`${id}-${handle.id}`}\n          style={{\n            ...handleStyle,\n            ...handle.style\n          }}\n        />\n      ))}\n\n      {/* Title */}\n      {title && (\n        <div style={titleStyle}>\n          <span>{title}</span>\n        </div>\n      )}\n\n      {/* Content */}\n      <div style={contentStyle}>\n        {children}\n      </div>\n    </div>\n  );\n};\n\n// Helper function to create handle configurations\nexport const createHandle = (id, type, position, style = {}) => ({\n  id,\n  type,\n  position,\n  style\n});\n\n// Common handle configurations\nexport const HANDLE_CONFIGS = {\n  sourceRight: (handleId = 'output') => createHandle(handleId, 'source', Position.Right),\n  targetLeft: (handleId = 'input') => createHandle(handleId, 'target', Position.Left),\n  targetTop: (handleId = 'input') => createHandle(handleId, 'target', Position.Top),\n  sourceBottom: (handleId = 'output') => createHandle(handleId, 'source', Position.Bottom),\n};\n\n// Common label styling for form inputs - extracted to maintain consistency\nexport const commonLabelStyle = {\n  fontSize: '12px',\n  display: 'flex',\n  flexDirection: 'column',\n  gap: '4px',\n  color: '#ffffff',\n  fontWeight: '600'\n};\n"], "mappings": ";AAAA;AACA;;AAEA,SAASA,MAAM,EAAEC,QAAQ,QAAQ,WAAW;AAC5C,SAASC,KAAK,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,OAAO,MAAMC,QAAQ,GAAGA,CAAC;EACvBC,EAAE;EACFC,IAAI;EACJC,KAAK;EACLC,QAAQ;EACRC,OAAO,GAAG,EAAE;EACZC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,GAAG;EACXC,MAAM,GAAG,EAAE;EACXC,QAAQ,GAAG;AACb,CAAC,KAAK;EAGJ,MAAMC,YAAY,GAAG;IACnBH,KAAK;IACLI,SAAS,EAAEH,MAAM;IACjBI,UAAU,EAAE,iFAAiF;IAC7FC,MAAM,EAAE,mCAAmC;IAC3CC,YAAY,EAAE,MAAM;IACpBC,OAAO,EAAEnB,KAAK,CAACoB,OAAO,CAACC,EAAE;IACzBC,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBC,cAAc,EAAE,YAAY;IAC5BC,UAAU,EAAE,SAAS;IACrBC,UAAU,EAAE,wCAAwC;IACpDC,UAAU,EAAE,2CAA2C;IACvDC,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,SAAS;IAChBC,cAAc,EAAE,YAAY;IAC5BC,SAAS,EAAE,oCAAoC;IAC/CC,SAAS,EAAE,YAAY;IACvB,GAAGvB;EACL,CAAC;EAED,MAAMwB,UAAU,GAAG;IACjBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,MAAM;IAChBN,KAAK,EAAE,SAAS;IAChBO,YAAY,EAAE,KAAK;IACnBC,SAAS,EAAE,QAAQ;IACnBC,YAAY,EAAE,mCAAmC;IACjDC,aAAa,EAAE,KAAK;IACpBC,aAAa,EAAE,WAAW;IAC1BC,aAAa,EAAE,OAAO;IACtBzB,UAAU,EAAE,0CAA0C;IACtD0B,cAAc,EAAE,MAAM;IACtBC,oBAAoB,EAAE,MAAM;IAC5BC,mBAAmB,EAAE;EACvB,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBC,IAAI,EAAE,CAAC;IACPxB,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBC,cAAc,EAAE,YAAY;IAC5BW,QAAQ,EAAEnC,KAAK,CAAC+C,UAAU,CAACZ,QAAQ,CAACa,EAAE;IACtCC,GAAG,EAAEjD,KAAK,CAACoB,OAAO,CAACC,EAAE;IACrB6B,UAAU,EAAElD,KAAK,CAACoB,OAAO,CAAC4B;EAC5B,CAAC;EAED,MAAMG,WAAW,GAAG;IAClBxC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdI,UAAU,EAAE,mDAAmD;IAC/DC,MAAM,EAAE,mBAAmB;IAC3BC,YAAY,EAAE,KAAK;IACnBQ,UAAU,EAAE,wCAAwC;IACpDK,SAAS,EAAE;EACb,CAAC;EAED,oBACE7B,OAAA;IACEO,KAAK,EAAEK,YAAa;IACpBJ,SAAS,EAAG,aAAYA,SAAU,EAAE;IACpC0C,YAAY,EAAGC,CAAC,IAAK;MACnBA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC8C,SAAS,GAAG,8BAA8B;MAChEF,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC+C,WAAW,GAAG,yBAAyB;MAC7DH,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACO,UAAU,GAAG,iFAAiF;MACpHqC,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACsB,SAAS,GAAG,qCAAqC;IACzE,CAAE;IACF0B,YAAY,EAAGJ,CAAC,IAAK;MACnBA,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC8C,SAAS,GAAG,wBAAwB;MAC1DF,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAAC+C,WAAW,GAAG,yBAAyB;MAC7DH,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACO,UAAU,GAAG,+EAA+E;MAClHqC,CAAC,CAACC,aAAa,CAAC7C,KAAK,CAACsB,SAAS,GAAG,oCAAoC;IACxE,CAAE;IAAAxB,QAAA,GAGDC,OAAO,CAACkD,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzB1D,OAAA,CAACJ,MAAM;MAEL+D,IAAI,EAAEF,MAAM,CAACE,IAAK;MAClBjC,QAAQ,EAAE+B,MAAM,CAAC/B,QAAS;MAC1BxB,EAAE,EAAG,GAAEA,EAAG,IAAGuD,MAAM,CAACvD,EAAG,EAAE;MACzBK,KAAK,EAAE;QACL,GAAG0C,WAAW;QACd,GAAGQ,MAAM,CAAClD;MACZ;IAAE,GAPI,GAAEL,EAAG,IAAGuD,MAAM,CAACvD,EAAE,IAAIwD,KAAM,EAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAQnC,CACF,CAAC,EAGD3D,KAAK,iBACJJ,OAAA;MAAKO,KAAK,EAAEwB,UAAW;MAAA1B,QAAA,eACrBL,OAAA;QAAAK,QAAA,EAAOD;MAAK;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGD/D,OAAA;MAAKO,KAAK,EAAEoC,YAAa;MAAAtC,QAAA,EACtBA;IAAQ;MAAAuD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAC,EAAA,GArHa/D,QAAQ;AAsHrB,OAAO,MAAMgE,YAAY,GAAGA,CAAC/D,EAAE,EAAEyD,IAAI,EAAEjC,QAAQ,EAAEnB,KAAK,GAAG,CAAC,CAAC,MAAM;EAC/DL,EAAE;EACFyD,IAAI;EACJjC,QAAQ;EACRnB;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAM2D,cAAc,GAAG;EAC5BC,WAAW,EAAEA,CAACC,QAAQ,GAAG,QAAQ,KAAKH,YAAY,CAACG,QAAQ,EAAE,QAAQ,EAAEvE,QAAQ,CAACwE,KAAK,CAAC;EACtFC,UAAU,EAAEA,CAACF,QAAQ,GAAG,OAAO,KAAKH,YAAY,CAACG,QAAQ,EAAE,QAAQ,EAAEvE,QAAQ,CAAC0E,IAAI,CAAC;EACnFC,SAAS,EAAEA,CAACJ,QAAQ,GAAG,OAAO,KAAKH,YAAY,CAACG,QAAQ,EAAE,QAAQ,EAAEvE,QAAQ,CAAC4E,GAAG,CAAC;EACjFC,YAAY,EAAEA,CAACN,QAAQ,GAAG,QAAQ,KAAKH,YAAY,CAACG,QAAQ,EAAE,QAAQ,EAAEvE,QAAQ,CAAC8E,MAAM;AACzF,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAG;EAC9B3C,QAAQ,EAAE,MAAM;EAChBb,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,QAAQ;EACvB0B,GAAG,EAAE,KAAK;EACVpB,KAAK,EAAE,SAAS;EAChBK,UAAU,EAAE;AACd,CAAC;AAAC,IAAAgC,EAAA;AAAAa,YAAA,CAAAb,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}