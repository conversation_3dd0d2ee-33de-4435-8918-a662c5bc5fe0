import { PipelineToolbar } from './toolbar';
import { PipelineUI } from './ui';
import { SubmitButton } from './submit';

// Debug logging for image/resource loading issues
console.log('App.js loaded successfully');

// Check if fonts are loading
document.fonts.ready.then(() => {
  console.log('Fonts loaded successfully');
}).catch((error) => {
  console.error('Font loading error:', error);
});

function App() {
  return (
    <div className="app-container">
      <PipelineToolbar />
      <PipelineUI />
      <SubmitButton />
    </div>
  );
}

export default App;
