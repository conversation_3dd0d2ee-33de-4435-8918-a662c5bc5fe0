{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\inputNode.js\",\n  _s = $RefreshSig$();\n// inputNode.js\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS } from './BaseNode';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const InputNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [currName, setCurrName] = useState((data === null || data === void 0 ? void 0 : data.inputName) || id.replace('customInput-', 'input_'));\n  const [inputType, setInputType] = useState(data.inputType || 'Text');\n  const [selectedFile, setSelectedFile] = useState(null);\n  const handleNameChange = e => {\n    setCurrName(e.target.value);\n  };\n  const handleTypeChange = e => {\n    setInputType(e.target.value);\n  };\n  const handleFileUpload = e => {\n    const file = e.target.files[0];\n    setSelectedFile(file);\n  };\n  const handles = [HANDLE_CONFIGS.sourceRight(`${id}-value`)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Input\",\n    handles: handles,\n    nodeType: \"input\",\n    height: 160,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"node-sections\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"node-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: \"Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: currName,\n          onChange: handleNameChange,\n          placeholder: \"Enter input name\",\n          className: \"section-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"node-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: \"Type\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: inputType,\n          onChange: handleTypeChange,\n          className: \"section-select\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Text\",\n            children: \"Text\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"File\",\n            children: \"File\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), inputType === 'File' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"node-section upload-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"upload-label\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            onChange: handleFileUpload,\n            style: {\n              display: 'none'\n            },\n            accept: \"*/*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"upload-button\",\n            children: \"Upload file\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 13\n        }, this), selectedFile && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"file-info\",\n          children: selectedFile.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_s(InputNode, \"YWtzDnRHptBMYY9BnhUhHLwinNY=\");\n_c = InputNode;\nvar _c;\n$RefreshReg$(_c, \"InputNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "HANDLE_CONFIGS", "jsxDEV", "_jsxDEV", "InputNode", "id", "data", "_s", "currName", "setCurrName", "inputName", "replace", "inputType", "setInputType", "selectedFile", "setSelectedFile", "handleNameChange", "e", "target", "value", "handleTypeChange", "handleFileUpload", "file", "files", "handles", "sourceRight", "title", "nodeType", "height", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onChange", "placeholder", "style", "display", "accept", "name", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/inputNode.js"], "sourcesContent": ["// inputNode.js\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS } from './BaseNode';\n\nexport const InputNode = ({ id, data }) => {\n  const [currName, setCurrName] = useState(data?.inputName || id.replace('customInput-', 'input_'));\n  const [inputType, setInputType] = useState(data.inputType || 'Text');\n  const [selectedFile, setSelectedFile] = useState(null);\n\n  const handleNameChange = (e) => {\n    setCurrName(e.target.value);\n  };\n\n  const handleTypeChange = (e) => {\n    setInputType(e.target.value);\n  };\n\n  const handleFileUpload = (e) => {\n    const file = e.target.files[0];\n    setSelectedFile(file);\n  };\n\n  const handles = [HANDLE_CONFIGS.sourceRight(`${id}-value`)];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Input\"\n      handles={handles}\n      nodeType=\"input\"\n      height={160}\n    >\n      <div className=\"node-sections\">\n        {/* Name Section */}\n        <div className=\"node-section\">\n          <div className=\"section-header\">Name</div>\n          <input\n            type=\"text\"\n            value={currName}\n            onChange={handleNameChange}\n            placeholder=\"Enter input name\"\n            className=\"section-input\"\n          />\n        </div>\n\n        {/* Type Section */}\n        <div className=\"node-section\">\n          <div className=\"section-header\">Type</div>\n          <select\n            value={inputType}\n            onChange={handleTypeChange}\n            className=\"section-select\"\n          >\n            <option value=\"Text\">Text</option>\n            <option value=\"File\">File</option>\n          </select>\n        </div>\n\n        {/* File Upload Section - Only show when File type is selected */}\n        {inputType === 'File' && (\n          <div className=\"node-section upload-section\">\n            <label className=\"upload-label\">\n              <input\n                type=\"file\"\n                onChange={handleFileUpload}\n                style={{ display: 'none' }}\n                accept=\"*/*\"\n              />\n              <div className=\"upload-button\">\n                Upload file\n              </div>\n            </label>\n            {selectedFile && (\n              <div className=\"file-info\">\n                {selectedFile.name}\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </BaseNode>\n  );\n}\n"], "mappings": ";;AAAA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,cAAc,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,OAAO,MAAMC,SAAS,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,CAAAO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,SAAS,KAAIL,EAAE,CAACM,OAAO,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;EACjG,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAACO,IAAI,CAACM,SAAS,IAAI,MAAM,CAAC;EACpE,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMiB,gBAAgB,GAAIC,CAAC,IAAK;IAC9BR,WAAW,CAACQ,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMC,gBAAgB,GAAIH,CAAC,IAAK;IAC9BJ,YAAY,CAACI,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAME,gBAAgB,GAAIJ,CAAC,IAAK;IAC9B,MAAMK,IAAI,GAAGL,CAAC,CAACC,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC;IAC9BR,eAAe,CAACO,IAAI,CAAC;EACvB,CAAC;EAED,MAAME,OAAO,GAAG,CAACvB,cAAc,CAACwB,WAAW,CAAE,GAAEpB,EAAG,QAAO,CAAC,CAAC;EAE3D,oBACEF,OAAA,CAACH,QAAQ;IACPK,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXoB,KAAK,EAAC,OAAO;IACbF,OAAO,EAAEA,OAAQ;IACjBG,QAAQ,EAAC,OAAO;IAChBC,MAAM,EAAE,GAAI;IAAAC,QAAA,eAEZ1B,OAAA;MAAK2B,SAAS,EAAC,eAAe;MAAAD,QAAA,gBAE5B1B,OAAA;QAAK2B,SAAS,EAAC,cAAc;QAAAD,QAAA,gBAC3B1B,OAAA;UAAK2B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1C/B,OAAA;UACEgC,IAAI,EAAC,MAAM;UACXhB,KAAK,EAAEX,QAAS;UAChB4B,QAAQ,EAAEpB,gBAAiB;UAC3BqB,WAAW,EAAC,kBAAkB;UAC9BP,SAAS,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN/B,OAAA;QAAK2B,SAAS,EAAC,cAAc;QAAAD,QAAA,gBAC3B1B,OAAA;UAAK2B,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1C/B,OAAA;UACEgB,KAAK,EAAEP,SAAU;UACjBwB,QAAQ,EAAEhB,gBAAiB;UAC3BU,SAAS,EAAC,gBAAgB;UAAAD,QAAA,gBAE1B1B,OAAA;YAAQgB,KAAK,EAAC,MAAM;YAAAU,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClC/B,OAAA;YAAQgB,KAAK,EAAC,MAAM;YAAAU,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLtB,SAAS,KAAK,MAAM,iBACnBT,OAAA;QAAK2B,SAAS,EAAC,6BAA6B;QAAAD,QAAA,gBAC1C1B,OAAA;UAAO2B,SAAS,EAAC,cAAc;UAAAD,QAAA,gBAC7B1B,OAAA;YACEgC,IAAI,EAAC,MAAM;YACXC,QAAQ,EAAEf,gBAAiB;YAC3BiB,KAAK,EAAE;cAAEC,OAAO,EAAE;YAAO,CAAE;YAC3BC,MAAM,EAAC;UAAK;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACF/B,OAAA;YAAK2B,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAE/B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACPpB,YAAY,iBACXX,OAAA;UAAK2B,SAAS,EAAC,WAAW;UAAAD,QAAA,EACvBf,YAAY,CAAC2B;QAAI;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAA3B,EAAA,CA/EYH,SAAS;AAAAsC,EAAA,GAATtC,SAAS;AAAA,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}