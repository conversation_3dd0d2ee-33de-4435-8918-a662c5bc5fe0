{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\inputNode.js\",\n  _s = $RefreshSig$();\n// inputNode.js\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS } from './BaseNode';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const InputNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const handleFileUpload = e => {\n    const file = e.target.files[0];\n    setSelectedFile(file);\n  };\n  const handles = [HANDLE_CONFIGS.sourceRight(`${id}-value`)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Input\",\n    handles: handles,\n    nodeType: \"input\",\n    height: 120,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"input-node-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"input-section-header\",\n          children: \"File\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-section upload-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"upload-label\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"file\",\n            onChange: handleFileUpload,\n            style: {\n              display: 'none'\n            },\n            accept: \"*/*\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"upload-button\",\n            children: \"Upload file\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), selectedFile && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"file-info\",\n          children: selectedFile.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_s(InputNode, \"cz3B5sQOxlrekAnUFUPeMaA2gqY=\");\n_c = InputNode;\nvar _c;\n$RefreshReg$(_c, \"InputNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "HANDLE_CONFIGS", "jsxDEV", "_jsxDEV", "InputNode", "id", "data", "_s", "selectedFile", "setSelectedFile", "handleFileUpload", "e", "file", "target", "files", "handles", "sourceRight", "title", "nodeType", "height", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onChange", "style", "display", "accept", "name", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/inputNode.js"], "sourcesContent": ["// inputNode.js\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS } from './BaseNode';\n\nexport const InputNode = ({ id, data }) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n\n  const handleFileUpload = (e) => {\n    const file = e.target.files[0];\n    setSelectedFile(file);\n  };\n\n  const handles = [HANDLE_CONFIGS.sourceRight(`${id}-value`)];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Input\"\n      handles={handles}\n      nodeType=\"input\"\n      height={120}\n    >\n      <div className=\"input-node-content\">\n        {/* File Section */}\n        <div className=\"input-section\">\n          <div className=\"input-section-header\">\n            File\n          </div>\n        </div>\n\n        {/* Upload File Section */}\n        <div className=\"input-section upload-section\">\n          <label className=\"upload-label\">\n            <input\n              type=\"file\"\n              onChange={handleFileUpload}\n              style={{ display: 'none' }}\n              accept=\"*/*\"\n            />\n            <div className=\"upload-button\">\n              Upload file\n            </div>\n          </label>\n          {selectedFile && (\n            <div className=\"file-info\">\n              {selectedFile.name}\n            </div>\n          )}\n        </div>\n      </div>\n    </BaseNode>\n  );\n}\n"], "mappings": ";;AAAA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,cAAc,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,OAAO,MAAMC,SAAS,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMW,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9BL,eAAe,CAACG,IAAI,CAAC;EACvB,CAAC;EAED,MAAMG,OAAO,GAAG,CAACd,cAAc,CAACe,WAAW,CAAE,GAAEX,EAAG,QAAO,CAAC,CAAC;EAE3D,oBACEF,OAAA,CAACH,QAAQ;IACPK,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXW,KAAK,EAAC,OAAO;IACbF,OAAO,EAAEA,OAAQ;IACjBG,QAAQ,EAAC,OAAO;IAChBC,MAAM,EAAE,GAAI;IAAAC,QAAA,eAEZjB,OAAA;MAAKkB,SAAS,EAAC,oBAAoB;MAAAD,QAAA,gBAEjCjB,OAAA;QAAKkB,SAAS,EAAC,eAAe;QAAAD,QAAA,eAC5BjB,OAAA;UAAKkB,SAAS,EAAC,sBAAsB;UAAAD,QAAA,EAAC;QAEtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtB,OAAA;QAAKkB,SAAS,EAAC,8BAA8B;QAAAD,QAAA,gBAC3CjB,OAAA;UAAOkB,SAAS,EAAC,cAAc;UAAAD,QAAA,gBAC7BjB,OAAA;YACEuB,IAAI,EAAC,MAAM;YACXC,QAAQ,EAAEjB,gBAAiB;YAC3BkB,KAAK,EAAE;cAAEC,OAAO,EAAE;YAAO,CAAE;YAC3BC,MAAM,EAAC;UAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACFtB,OAAA;YAAKkB,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAE/B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EACPjB,YAAY,iBACXL,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAD,QAAA,EACvBZ,YAAY,CAACuB;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAAlB,EAAA,CAjDYH,SAAS;AAAA4B,EAAA,GAAT5B,SAAS;AAAA,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}