// outputNode.js

import { useState } from 'react';
import { BaseNode, HANDLE_CONFIGS, commonLabelStyle } from './BaseNode';
import { NodeInput } from '../components/NodeInput';

export const OutputNode = ({ id, data }) => {
  const [currName, setCurrName] = useState(data?.outputName || id.replace('customOutput-', 'output_'));
  const [outputType, setOutputType] = useState(data.outputType || 'Text');

  const handleNameChange = (e) => {
    setCurrName(e.target.value);
  };

  const handleTypeChange = (e) => {
    setOutputType(e.target.value);
  };

  const handles = [HANDLE_CONFIGS.targetLeft(`${id}-value`)];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Output"
      handles={handles}
      nodeType="output"
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <label style={commonLabelStyle}>
          Name:
          <NodeInput
            value={currName}
            onChange={handleNameChange}
            placeholder="Enter output name"
          />
        </label>
        <label style={commonLabelStyle}>
          Type:
          <select
            value={outputType}
            onChange={handleTypeChange}
            className="node-input"
            style={{ cursor: 'pointer' }}
          >
            <option value="Text">Text</option>
            <option value="File">Image</option>
          </select>
        </label>
      </div>
    </BaseNode>
  );
}
