{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\llmNode.js\";\n// llmNode.js\n\nimport { BaseNode, createHandle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const LLMNode = ({\n  id,\n  data\n}) => {\n  const handles = [createHandle(`${id}-system`, 'target', Position.Left, {\n    top: `${100 / 3}%`\n  }), createHandle(`${id}-prompt`, 'target', Position.Left, {\n    top: `${200 / 3}%`\n  }), createHandle(`${id}-response`, 'source', Position.Right)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"LLM\",\n    handles: handles,\n    nodeType: \"llm\",\n    height: 100,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"node-sections\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"node-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-header\",\n          children: \"Model\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"model-description\",\n          children: \"Large Language Model\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_c = LLMNode;\nvar _c;\n$RefreshReg$(_c, \"LLMNode\");", "map": {"version": 3, "names": ["BaseNode", "createHandle", "Position", "jsxDEV", "_jsxDEV", "LLMNode", "id", "data", "handles", "Left", "top", "Right", "title", "nodeType", "height", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/llmNode.js"], "sourcesContent": ["// llmNode.js\n\nimport { BaseNode, createHandle } from './BaseNode';\nimport { Position } from 'reactflow';\n\nexport const LLMNode = ({ id, data }) => {\n  const handles = [\n    createHandle(`${id}-system`, 'target', Position.Left, { top: `${100/3}%` }),\n    createHandle(`${id}-prompt`, 'target', Position.Left, { top: `${200/3}%` }),\n    createHandle(`${id}-response`, 'source', Position.Right)\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"LLM\"\n      handles={handles}\n      nodeType=\"llm\"\n      height={100}\n    >\n      <div className=\"node-sections\">\n        {/* Model Section */}\n        <div className=\"node-section\">\n          <div className=\"section-header\">Model</div>\n          <div className=\"model-description\">\n            Large Language Model\n          </div>\n        </div>\n      </div>\n    </BaseNode>\n  );\n}\n"], "mappings": ";AAAA;;AAEA,SAASA,QAAQ,EAAEC,YAAY,QAAQ,YAAY;AACnD,SAASC,QAAQ,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,OAAO,MAAMC,OAAO,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EACvC,MAAMC,OAAO,GAAG,CACdP,YAAY,CAAE,GAAEK,EAAG,SAAQ,EAAE,QAAQ,EAAEJ,QAAQ,CAACO,IAAI,EAAE;IAAEC,GAAG,EAAG,GAAE,GAAG,GAAC,CAAE;EAAG,CAAC,CAAC,EAC3ET,YAAY,CAAE,GAAEK,EAAG,SAAQ,EAAE,QAAQ,EAAEJ,QAAQ,CAACO,IAAI,EAAE;IAAEC,GAAG,EAAG,GAAE,GAAG,GAAC,CAAE;EAAG,CAAC,CAAC,EAC3ET,YAAY,CAAE,GAAEK,EAAG,WAAU,EAAE,QAAQ,EAAEJ,QAAQ,CAACS,KAAK,CAAC,CACzD;EAED,oBACEP,OAAA,CAACJ,QAAQ;IACPM,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXK,KAAK,EAAC,KAAK;IACXJ,OAAO,EAAEA,OAAQ;IACjBK,QAAQ,EAAC,KAAK;IACdC,MAAM,EAAE,GAAI;IAAAC,QAAA,eAEZX,OAAA;MAAKY,SAAS,EAAC,eAAe;MAAAD,QAAA,eAE5BX,OAAA;QAAKY,SAAS,EAAC,cAAc;QAAAD,QAAA,gBAC3BX,OAAA;UAAKY,SAAS,EAAC,gBAAgB;UAAAD,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3ChB,OAAA;UAAKY,SAAS,EAAC,mBAAmB;UAAAD,QAAA,EAAC;QAEnC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAAAC,EAAA,GA3BYhB,OAAO;AAAA,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}