{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\switchNode.js\",\n  _s = $RefreshSig$();\n// switchNode.js\n// Demonstrates conditional routing with multiple outputs\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle } from './BaseNode';\nimport { Position } from 'reactflow';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const SwitchNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [condition, setCondition] = useState((data === null || data === void 0 ? void 0 : data.condition) || 'true');\n  const handleConditionChange = e => {\n    setCondition(e.target.value);\n  };\n  const handles = [createHandle(`${id}-input`, 'target', Position.Left), createHandle(`${id}-condition`, 'target', Position.Top), createHandle(`${id}-true`, 'source', Position.Right, {\n    top: '30%'\n  }), createHandle(`${id}-false`, 'source', Position.Right, {\n    top: '70%'\n  })];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Switch\",\n    handles: handles,\n    nodeType: \"switch\",\n    height: 90,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"inline-label\",\n        children: [\"Default:\", /*#__PURE__*/_jsxDEV(\"select\", {\n          value: condition,\n          onChange: handleConditionChange,\n          className: \"node-input\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"true\",\n            children: \"True\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"false\",\n            children: \"False\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: 'rgba(255, 255, 255, 0.6)'\n        },\n        children: \"Routes based on condition\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_s(SwitchNode, \"8qbhjYQ3rNaRhZI97gO2kO7T3ms=\");\n_c = SwitchNode;\nvar _c;\n$RefreshReg$(_c, \"SwitchNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "createHandle", "Position", "jsxDEV", "_jsxDEV", "SwitchNode", "id", "data", "_s", "condition", "setCondition", "handleConditionChange", "e", "target", "value", "handles", "Left", "Top", "Right", "top", "title", "nodeType", "height", "children", "className", "onChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "fontSize", "color", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/switchNode.js"], "sourcesContent": ["// switchNode.js\n// Demonstrates conditional routing with multiple outputs\n\nimport { useState } from 'react';\nimport { BaseNode, createHandle } from './BaseNode';\nimport { Position } from 'reactflow';\n\nexport const SwitchNode = ({ id, data }) => {\n  const [condition, setCondition] = useState(data?.condition || 'true');\n\n  const handleConditionChange = (e) => {\n    setCondition(e.target.value);\n  };\n\n  const handles = [\n    createHandle(`${id}-input`, 'target', Position.Left),\n    createHandle(`${id}-condition`, 'target', Position.Top),\n    createHandle(`${id}-true`, 'source', Position.Right, { top: '30%' }),\n    createHandle(`${id}-false`, 'source', Position.Right, { top: '70%' })\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Switch\"\n      handles={handles}\n      nodeType=\"switch\"\n      height={90}\n    >\n      <div>\n        <label className=\"inline-label\">\n          Default:\n          <select\n            value={condition}\n            onChange={handleConditionChange}\n            className=\"node-input\"\n          >\n            <option value=\"true\">True</option>\n            <option value=\"false\">False</option>\n          </select>\n        </label>\n        <div style={{ fontSize: '10px', color: 'rgba(255, 255, 255, 0.6)' }}>\n          Routes based on condition\n        </div>\n      </div>\n    </BaseNode>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,YAAY;AACnD,SAASC,QAAQ,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,OAAO,MAAMC,UAAU,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC1C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,CAAAQ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,SAAS,KAAI,MAAM,CAAC;EAErE,MAAME,qBAAqB,GAAIC,CAAC,IAAK;IACnCF,YAAY,CAACE,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAMC,OAAO,GAAG,CACdd,YAAY,CAAE,GAAEK,EAAG,QAAO,EAAE,QAAQ,EAAEJ,QAAQ,CAACc,IAAI,CAAC,EACpDf,YAAY,CAAE,GAAEK,EAAG,YAAW,EAAE,QAAQ,EAAEJ,QAAQ,CAACe,GAAG,CAAC,EACvDhB,YAAY,CAAE,GAAEK,EAAG,OAAM,EAAE,QAAQ,EAAEJ,QAAQ,CAACgB,KAAK,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,EACpElB,YAAY,CAAE,GAAEK,EAAG,QAAO,EAAE,QAAQ,EAAEJ,QAAQ,CAACgB,KAAK,EAAE;IAAEC,GAAG,EAAE;EAAM,CAAC,CAAC,CACtE;EAED,oBACEf,OAAA,CAACJ,QAAQ;IACPM,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXa,KAAK,EAAC,QAAQ;IACdL,OAAO,EAAEA,OAAQ;IACjBM,QAAQ,EAAC,QAAQ;IACjBC,MAAM,EAAE,EAAG;IAAAC,QAAA,eAEXnB,OAAA;MAAAmB,QAAA,gBACEnB,OAAA;QAAOoB,SAAS,EAAC,cAAc;QAAAD,QAAA,GAAC,UAE9B,eAAAnB,OAAA;UACEU,KAAK,EAAEL,SAAU;UACjBgB,QAAQ,EAAEd,qBAAsB;UAChCa,SAAS,EAAC,YAAY;UAAAD,QAAA,gBAEtBnB,OAAA;YAAQU,KAAK,EAAC,MAAM;YAAAS,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClCzB,OAAA;YAAQU,KAAK,EAAC,OAAO;YAAAS,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACRzB,OAAA;QAAK0B,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEC,KAAK,EAAE;QAA2B,CAAE;QAAAT,QAAA,EAAC;MAErE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAACrB,EAAA,CAzCWH,UAAU;AAAA4B,EAAA,GAAV5B,UAAU;AAAA,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}